import 'package:equatable/equatable.dart';

class Product extends Equatable {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final double price;
  final String currency;
  final double? originalPrice;
  final double? rating;
  final int? reviewCount;
  final String? category;
  final bool isInStock;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Product({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.price,
    this.currency = 'USD',
    this.originalPrice,
    this.rating,
    this.reviewCount,
    this.category,
    this.isInStock = true,
    this.createdAt,
    this.updatedAt,
  });

  bool get hasDiscount => originalPrice != null && originalPrice! > price;

  double? get discountPercentage {
    if (!hasDiscount) return null;
    return ((originalPrice! - price) / originalPrice!) * 100;
  }

  String get formattedPrice {
    return '\$${price.toStringAsFixed(2)}';
  }

  String? get formattedOriginalPrice {
    if (originalPrice == null) return null;
    return '\$${originalPrice!.toStringAsFixed(2)}';
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        imageUrl,
        price,
        currency,
        originalPrice,
        rating,
        reviewCount,
        category,
        isInStock,
        createdAt,
        updatedAt,
      ];
}
