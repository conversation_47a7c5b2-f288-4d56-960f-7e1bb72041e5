import 'package:broker_app/features/home/<USER>/models/product_model.dart';

import '../models/product_model.dart';

abstract class ProductRemoteDataSource {
  /// Get all products from Supabase
  Future<List<ProductModel>> getProducts();
  
  /// Get a specific product by ID from Supabase
  Future<ProductModel> getProductById(String id);
  
  /// Search products by query from Supabase
  Future<List<ProductModel>> searchProducts(String query);
}
