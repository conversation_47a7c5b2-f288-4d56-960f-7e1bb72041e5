import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../domain/entities/product.dart';
import '../../domain/usecases/get_product_details.dart';

part 'product_details_event.dart';
part 'product_details_state.dart';

class ProductDetailsBloc extends Bloc<ProductDetailsEvent, ProductDetailsState> {
  final GetProductDetails getProductDetails;

  ProductDetailsBloc({
    required this.getProductDetails,
  }) : super(ProductDetailsInitial()) {
    on<LoadProductDetails>(_onLoadProductDetails);
    on<RefreshProductDetails>(_onRefreshProductDetails);
  }

  Future<void> _onLoadProductDetails(
    LoadProductDetails event,
    Emitter<ProductDetailsState> emit,
  ) async {
    emit(ProductDetailsLoading());
    
    final result = await getProductDetails(
      GetProductDetailsParams(productId: event.productId),
    );
    
    result.fold(
      (failure) => emit(ProductDetailsError(message: failure.message)),
      (product) => emit(ProductDetailsLoaded(product: product)),
    );
  }

  Future<void> _onRefreshProductDetails(
    RefreshProductDetails event,
    Emitter<ProductDetailsState> emit,
  ) async {
    // Keep current state while refreshing
    final currentState = state;
    if (currentState is ProductDetailsLoaded) {
      emit(ProductDetailsRefreshing(product: currentState.product));
    } else {
      emit(ProductDetailsLoading());
    }
    
    final result = await getProductDetails(
      GetProductDetailsParams(productId: event.productId),
    );
    
    result.fold(
      (failure) => emit(ProductDetailsError(message: failure.message)),
      (product) => emit(ProductDetailsLoaded(product: product)),
    );
  }
}
