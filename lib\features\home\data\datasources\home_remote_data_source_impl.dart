import 'dart:developer';

import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/error/exceptions.dart';
import '../models/offer_model.dart';
import '../models/platform_model.dart';
import '../models/product_model.dart';
import 'home_remote_data_source.dart';

class HomeRemoteDataSourceImpl implements HomeRemoteDataSource {
  final SupabaseClient supabaseClient;

  HomeRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<List<OfferModel>> getOffers() async {
    try {
      final response = await supabaseClient.from('offers').select('*');

      return (response as List<dynamic>)
          .map((json) => OfferModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } on PostgrestException catch (e) {
      throw ServerException(message: 'Database error: ${e.message}');
    } catch (e) {
      throw ServerException(message: 'Failed to fetch offers: $e');
    }
  }

  @override
  Future<List<PlatformModel>> getPlatforms() async {
    try {
      final response = await supabaseClient
          .from('platforms')
          .select('*')
          .order('name', ascending: true);

      return (response as List<dynamic>)
          .map((json) => PlatformModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } on PostgrestException catch (e) {
      throw ServerException(message: 'Database error: ${e.message}');
    } catch (e) {
      throw ServerException(message: 'Failed to fetch platforms: $e');
    }
  }

  @override
  Future<List<ProductModel>> getSuggestedProducts() async {
    try {
      final response = await supabaseClient.from('products').select('*');

      return (response).map((json) => ProductModel.fromMap(json)).toList();
    } on PostgrestException catch (e) {
      throw ServerException(message: 'Database error: ${e.message}');
    } catch (e) {
      throw ServerException(message: 'Failed to fetch suggested products: $e');
    }
  }

  @override
  Future<List<ProductModel>> searchProducts(String query) async {
    try {
      final response = await supabaseClient
          .from('products')
          .select('*')
          .or('name.ilike.%$query%,description.ilike.%$query%')
          .eq('is_in_stock', true)
          .order('name', ascending: true)
          .limit(50);

      return (response as List<dynamic>)
          .map((json) => ProductModel.fromMap(json as Map<String, dynamic>))
          .toList();
    } on PostgrestException catch (e) {
      throw ServerException(message: 'Database error: ${e.message}');
    } catch (e) {
      throw ServerException(message: 'Failed to search products: $e');
    }
  }
}
