import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/product.dart';
import '../../domain/usecases/get_products.dart';

part 'product_list_event.dart';
part 'product_list_state.dart';

class ProductListBloc extends Bloc<ProductListEvent, ProductListState> {
  final GetProducts getProducts;

  ProductListBloc({required this.getProducts}) : super(ProductListInitial()) {
    on<LoadProducts>(_onLoadProducts);
    on<RefreshProducts>(_onRefreshProducts);
  }

  Future<void> _onLoadProducts(
    LoadProducts event,
    Emitter<ProductListState> emit,
  ) async {
    emit(ProductListLoading());

    final result = await getProducts(NoParams());

    result.fold((failure) => emit(ProductListError(message: failure.message)), (
      products,
    ) {
      if (products.isEmpty) {
        emit(ProductListEmpty());
      } else {
        emit(ProductListLoaded(products: products));
      }
    });
  }

  Future<void> _onRefreshProducts(
    RefreshProducts event,
    Emitter<ProductListState> emit,
  ) async {
    // Keep current state while refreshing
    final currentState = state;
    if (currentState is ProductListLoaded) {
      emit(ProductListRefreshing(products: currentState.products));
    } else {
      emit(ProductListLoading());
    }

    final result = await getProducts(NoParams());

    result.fold((failure) => emit(ProductListError(message: failure.message)), (
      products,
    ) {
      if (products.isEmpty) {
        emit(ProductListEmpty());
      } else {
        emit(ProductListLoaded(products: products));
      }
    });
  }
}
