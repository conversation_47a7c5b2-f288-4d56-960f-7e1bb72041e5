import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../entities/product.dart';

abstract class ProductRepository {
  /// Get all products from the data source
  Future<Either<Failure, List<Product>>> getProducts();
  
  /// Get a specific product by its ID
  Future<Either<Failure, Product>> getProductById(String id);
  
  /// Search products by query
  Future<Either<Failure, List<Product>>> searchProducts(String query);
}
